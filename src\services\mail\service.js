const Responses = require("@common/Responses");
const MailSendTypes = require("@constants/MailSendTypes");
const Currencies = require("@common-constants/Currencies");
const MailAttachmentTypes = require("@common-constants/MailAttachmentTypes");
const Mail = require("@common-models/Mail");
const base = require("@mail-service/base");

async function sendMail(sendType, userIds, title, content, attachments) {
    const mail = new Mail();
    mail.setTitle(title);
    mail.setContent(content);
    mail.setCreationTime(Date.now());

    for (let i = 0; i < attachments.length; i++) {
        const reward = attachments[i];
        switch (reward.type) {
            case MailAttachmentTypes.CURRENCY:
                if (reward.currency != Currencies.GOLD && reward.currency != Currencies.DIAMOND) {
                    return Responses.invalidParameter();
                }

                if (isNaN(reward.quantity)) {
                    return Responses.invalidParameter();
                }

                mail.addAttachment(base.getCurrencyAttachment(reward.currency, reward.quantity));
                break;
            case MailAttachmentTypes.DRESS:
                if (isNaN(reward.dressId)) {
                    return Responses.invalidParameter();
                }

                mail.addAttachment(base.getDressAttachment(reward.dressId));
                break;
            case MailAttachmentTypes.VIP:
                if (isNaN(reward.vipLevel)) {
                    return Responses.invalidParameter();
                }

                if (isNaN(reward.vipDays)) {
                    return Responses.invalidParameter();
                }

                mail.addAttachment(base.getVipAttachment(reward.vipLevel, reward.vipDays));
                break;
        }
    }

    const mailId = await mail.save();
    if (!mailId) {
        return Responses.failed();
    }

    if (sendType == MailSendTypes.EVERYONE) {
        await mail.sendEveryone(mailId);
    } else {
        await mail.sendUsers(mailId, userIds);
    }

    return Responses.success();
}

module.exports = {
    sendMail: sendMail
}