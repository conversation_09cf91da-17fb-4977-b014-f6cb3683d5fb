const Responses = require("@common/Responses");
const service = require("@mail-service/service");

function sendMail(request) {
    const sendType = parseInt(request.body.sendType);
    if (isNaN(sendType) || (sendType != 0 && sendType != 1)) {
        return Responses.invalidParameter();
    }

    const userIds = request.body.userIds;
    if (userIds && !Array.isArray(userIds)) {
        return Responses.invalidParameter();
    }

    const title = request.body.title;
    if (!title) {
        return Responses.invalidParameter();
    }

    const content = request.body.content;
    if (!content) {
        return Responses.invalidParameter();
    }

    const attachments = request.body.attachments;
    if (attachments && !Array.isArray(attachments)) {
        return Responses.invalidParameter();
    }

    return service.sendMail(sendType, userIds, title, content, attachments);
}

module.exports = [
    {
        "path": "/mail/api/v1/send",
        "methods": ["POST"],
        "functions": [sendMail]
    }
]