const util = require("node:util");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const ServerTime = require("@common/ServerTime");
const clanConfig = require("@common-config/clan");
const ClanGoalTargets = require("@common-constants/ClanGoalTargets");
const ClanGoalTypes = require("@common-constants/ClanGoalTypes");
const ClanTaskTypes = require("@common-constants/ClanTaskTypes");
const ClanTaskStatuses = require("@common/constants/ClanTaskStatuses");
const ClanMember = require("@common-models/ClanMember");
const ClanTask = require("@common-models/ClanTask");
const Model = require("@common-models/Model");

module.exports = class SettlementRecord extends Model {
    constructor() {
        super();

        this.userId = 0;
        this.meanTime = 0; // time spent in the game
        this.kill = 0; // Score (don't be fooled by their stupid namings!)
        this.gameId = "";
        this.isCount = 0; // "Whether it is counted as a game, 0 means it is not counted as a game; 1 means it is counted as a game"
        this.rank = 0;
        this.integral = 0; // "Points, the points obtained this time, used for ranking"
        this.type = 0; // "Points ranking type, the default value is 0, indicating ranking by total; the default value is 1, indicating ranking by maximum value"
    }

    /** @returns {SettlementRecord} */
    static fromJson(json) {
        return super.fromJson(SettlementRecord, json);
    }

    async processForRanking() {
        const weekKey = util.format(RedisKeys.CACHE_GAME_WEEK_RANK, this.gameId);
        await Redis.incrementKeyScore(weekKey, this.userId, this.integral);
        await Redis.setExpire({ key: weekKey }, ServerTime.getWeekTimeLeft()); // Only set once for the whole week
        
        const monthKey = util.format(RedisKeys.CACHE_GAME_MONTH_RANK, this.gameId);
        await Redis.incrementKeyScore(monthKey, this.userId, this.integral);
        await Redis.setExpire({ key: monthKey }, ServerTime.getMonthTimeLeft()); // Only set once for the whole month

        const overallKey = util.format(RedisKeys.GAME_OVERALL_RANK, this.gameId);
        await Redis.incrementKeyScore(overallKey, this.userId, this.integral);
    }

    async processForClan() {
        const clanMember = await ClanMember.fromUserId(this.userId);
        if (!clanMember || !clanMember.getClanId()) {
            return;
        }

        const hasPersonalTasks = await ClanTask.hasTasks(this.userId, ClanTaskTypes.PERSONAL);
        if (hasPersonalTasks) {
            this.processForClanTasks(this.userId, ClanTaskTypes.PERSONAL);
        }

        const hasClanTasks = await ClanTask.hasTasks(clanMember.getClanId(), ClanTaskTypes.CLANWIDE);
        if (hasClanTasks) {
            this.processForClanTasks(clanMember.getClanId(), ClanTaskTypes.CLANWIDE);
        }
    }

    async processForClanTasks(masterId, taskType) {
        const maxTasks = taskType == ClanTaskTypes.PERSONAL ? clanConfig.maxPersonalTasks : clanConfig.maxClanTasks;
        const cacheKey = taskType == ClanTaskTypes.PERSONAL ? RedisKeys.CACHE_PERSONAL_CLAN_TASK : RedisKeys.CACHE_CLANWIDE_CLAN_TASK;
        const allTaskConfig = taskType == ClanTaskTypes.PERSONAL ? clanConfig.personalTasks : clanConfig.clanTasks;

        for (let taskIdx = 1; taskIdx <= maxTasks; taskIdx++) {
            const taskInfo = await Redis.getKey(cacheKey, masterId, taskIdx); // ID,Progress,Status
            const splitTaskInfo = taskInfo.split(',');
    
            const taskId = parseInt(splitTaskInfo[0]);
            const taskProgress = parseInt(splitTaskInfo[1]);
            const taskStatus = parseInt(splitTaskInfo[2]);
    
            if (taskStatus != ClanTaskStatuses.RUNNING) {
                continue;
            }

            const taskConfig = allTaskConfig[taskId];
            let newProgress = taskProgress;
            let newStatus = taskStatus;

            switch (taskConfig.type) {
                case ClanGoalTypes.PLAY_TIME:
                    newProgress += this.meanTime;
                    break;
                case ClanGoalTypes.KILL:
                    newProgress += this.kill;
                    break;
                case ClanGoalTypes.FIRST_RANK:
                    if (this.rank == 1) {
                        newProgress++;
                    }
                    break;
                case ClanGoalTypes.GAME_COUNT:
                    if (taskConfig.type == ClanGoalTargets.ANY || taskConfig.type == this.gameId)  {
                        newProgress++;
                    }
                    break;
                case ClanGoalTypes.KILL_STREAK:
                    if (this.kill >= taskConfig.value) {
                        newProgress++;
                    }
                    break;
            }

            newProgress = Math.min(taskConfig.value, newProgress);
            newStatus = (newProgress == taskConfig.value ? ClanTaskStatuses.FINISHED : ClanTaskStatuses.RUNNING);

            await Redis.setKey({ key: cacheKey, params: [masterId, taskIdx]}, `${taskId},${newProgress},${newStatus}`);
        }
    }

    setUserId(userId) {
        this.userId = userId;
    }

    getUserId() {
        return this.userId;
    }

    setMeanTime(meanTime) {
        this.meanTime = meanTime;
    }

    getMeanTime() {
        return this.meanTime;
    }

    setKill(kill) {
        this.kill = kill;
    }

    getKill() {
        return this.kill;
    }

    setGameId(gameId) {
        this.gameId = gameId;
    }

    getGameId() {
        return this.gameId;
    }

    setIsCount(isCount) {
        this.isCount = isCount;
    }
    
    getIsCount() {
        return this.isCount;
    }

    setRank(rank) {
        this.rank = rank;
    }

    getRank() {
        return this.rank;
    }

    setIntegral(integral) {
        this.integral = integral;
    }

    getIntegral() {
        return this.integral;
    }

    setType(type) {
        this.type = type;
    }

    getType() {
        return this.type;
    }
}