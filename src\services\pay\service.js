const Responses = require("@common/Responses");
const Wealth = require("@common-models/Wealth");
const PayService = require("@pay-service/base");

async function getUserWealth(userId) {
    const wealth = await Wealth.fromUserId(userId);
    return Responses.success(wealth);
}

async function buyGameProp(userId, currency, quantity, gameId, propId) {
    const { hasFailed, orderId, balance } = await PayService.removeCurrency(userId, currency, quantity);
    if (hasFailed) {
        return Responses.failed();
    }

    return Responses.success({
        diamonds: balance.getDiamonds(),
        golds: balance.getGold(),
        orderId: orderId
    })
}

module.exports = {
    getUserWealth: getUserWealth,
    buyGameProp: buyGameProp
}