{"name": "blockyforge-inner-api", "version": "1.0.0", "description": "Blocky Forge INNER(TM) APIs", "main": "src/app.js", "scripts": {"dev": "nodemon ."}, "_moduleAliases": {"@common": "../blockyforge-common/src/", "@common-config": "../blockyforge-common/src/config", "@common-constants": "../blockyforge-common/src/constants", "@common-models": "../blockyforge-common/src/models", "@constants": "src/constants", "@config": "src/config", "@models": "src/models", "@activity-service": "src/services/activity", "@user-service": "src/services/user", "@game-service": "src/services/game", "@mail-service": "src/services/mail", "@decoration-service": "src/services/decoration", "@pay-service": "src/services/pay", "@discord-service": "src/services/discord"}, "dependencies": {"fastify": "^5.3.3", "mariadb": "^3.2.3", "module-alias": "^2.2.3", "redis": "^4.6.13"}, "devDependencies": {"nodemon": "^3.1.0"}}