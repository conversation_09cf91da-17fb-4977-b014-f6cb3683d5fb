const Responses = require("@common/Responses");
const PlayerRecord = require("@models/PlayerRecord");
const SettlementRecord = require("@models/SettlementRecord");
const service = require("@game-service/service");

async function getPlayerSettlement(request) {
    const userId = parseInt(request.params.userId);
    if (!userId) {
        return Responses.invalidParameter();
    }

    if (!request.body) {
        return Responses.invalidParameter();
    }

    const data = PlayerRecord.fromJson(request.body);
    
    return await service.getPlayerSettlementGold(userId, data.getGoldReward());
}

async function getPlayerSettlementRecords(request) {
    let data = request.body;
    if (!Array.isArray(data)) {
        return Responses.invalidParameter();
    }

    data = data.map(x => PlayerRecord.fromJson(x));

    return await service.getPlayerSettlementRecords(data);
}

async function getPlayerSettlementGold(request) {
    const userId = parseInt(request.query.userId);
    if (!userId) {
        return Responses.invalidParameter();
    }

    const gold = parseInt(request.query.golds);
    if (!gold) {
        return Responses.invalidParameter();
    }

    return await service.getPlayerSettlementGold(userId, gold);
}

async function reportSettlementRecords(request) {
    let data = request.body;
    if (!Array.isArray(data)) {
        return Responses.invalidParameter();
    }

    data = data.map(x => SettlementRecord.fromJson(x));

    return await service.reportSettlementRecords(data);
}

module.exports = [
    {
        "path": "/game/api/v1/users/:userId/games/:gameId/settlement",
        "methods": ["POST"],
        "functions": [getPlayerSettlement]
    },
    {
        "path": "/game/api/v1/users/inner/games/settlement/list",
        "methods": ["POST"],
        "functions": [getPlayerSettlementRecords]
    },
    {
        "path": "/game/api/v1/users/games/settlement/golds",
        "methods": ["POST"],
        "functions": [getPlayerSettlementGold]
    },
    {
        "path": "/game/api/v1/inner/users/games/reporting/list",
        "methods": ["POST"],
        "functions": [reportSettlementRecords]
    }
]